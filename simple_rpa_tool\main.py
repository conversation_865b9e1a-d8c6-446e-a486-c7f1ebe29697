"""
Simple RPA Tool - 适用于小白的RPA工具
主程序入口
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# from gui.main_window import MainWindow
# from core.task_engine import TaskEngine
# from core.action_types import ActionType
# 修改这里的导入语句
from simple_rpa_tool.gui.main_window import MainWindow
from simple_rpa_tool.core.task_engine import TaskEngine
from simple_rpa_tool.core.action_types import ActionType

class SimpleRPATool:
    """简单RPA工具主类"""
    
    def __init__(self):
        """初始化RPA工具"""
        self.root = tk.Tk()
        self.root.title("Simple RPA Tool - 小白专用RPA工具")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置图标和样式
        self.setup_styles()
        
        # 初始化核心组件
        self.task_engine = TaskEngine()
        
        # 创建主窗口
        self.main_window = MainWindow(self.root, self.task_engine)
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_styles(self):
        """设置界面样式"""
        try:
            # 设置主题
            style = ttk.Style()
            style.theme_use('clam')
            
            # 自定义样式
            style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
            style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
            style.configure('Action.TButton', padding=(10, 5))
            
        except Exception as e:
            print(f"样式设置失败: {e}")
    
    def on_closing(self):
        """程序关闭时的处理"""
        try:
            # 停止所有正在运行的任务
            self.task_engine.stop_all_tasks()
            
            # 保存用户设置
            self.save_user_settings()
            
            # 关闭程序
            self.root.destroy()
            
        except Exception as e:
            print(f"关闭程序时出错: {e}")
            self.root.destroy()
    
    def save_user_settings(self):
        """保存用户设置"""
        try:
            settings = {
                'window_geometry': self.root.geometry(),
                'last_project_path': getattr(self.main_window, 'current_project_path', ''),
                'recent_projects': getattr(self.main_window, 'recent_projects', [])
            }
            
            config_dir = project_root / 'config'
            config_dir.mkdir(exist_ok=True)
            
            with open(config_dir / 'user_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存用户设置失败: {e}")
    
    def load_user_settings(self):
        """加载用户设置"""
        try:
            config_file = project_root / 'config' / 'user_settings.json'
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                # 恢复窗口大小和位置
                if 'window_geometry' in settings:
                    self.root.geometry(settings['window_geometry'])
                
                # 恢复最近项目
                if hasattr(self.main_window, 'load_recent_projects'):
                    self.main_window.load_recent_projects(settings.get('recent_projects', []))
                    
        except Exception as e:
            print(f"加载用户设置失败: {e}")
    
    def run(self):
        """运行RPA工具"""
        try:
            # 加载用户设置
            self.load_user_settings()
            
            # 显示欢迎信息
            self.show_welcome_message()
            
            # 启动主循环
            self.root.mainloop()
            
        except Exception as e:
            messagebox.showerror("启动失败", f"程序启动失败: {str(e)}")
    
    def show_welcome_message(self):
        """显示欢迎信息"""
        welcome_text = """
🎉 欢迎使用 Simple RPA Tool！

这是一个专为小白用户设计的RPA自动化工具，具有以下特点：

✨ 图形化界面，无需编程知识
🎯 拖拽式任务设计，简单直观
📝 操作录制功能，一键生成脚本
📦 一键打包为exe文件，方便分发
⏰ 定时任务调度，自动执行

快速开始：
1. 点击"新建任务"创建你的第一个RPA任务
2. 使用"录制"功能记录你的操作
3. 在任务设计器中编辑和优化流程
4. 点击"运行"测试你的任务
5. 使用"打包"功能生成exe文件

需要帮助？点击菜单栏的"帮助"查看详细教程。
        """
        
        # 在状态栏显示欢迎信息
        if hasattr(self.main_window, 'status_bar'):
            self.main_window.status_bar.config(text="欢迎使用 Simple RPA Tool！点击帮助查看使用教程。")


def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 7):
            messagebox.showerror("版本错误", "此程序需要Python 3.7或更高版本")
            return
        
        # 创建必要的目录
        for dir_name in ['config', 'templates', 'output', 'logs']:
            (project_root / dir_name).mkdir(exist_ok=True)
        
        # 启动RPA工具
        app = SimpleRPATool()
        app.run()
        
    except Exception as e:
        messagebox.showerror("启动失败", f"程序启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
