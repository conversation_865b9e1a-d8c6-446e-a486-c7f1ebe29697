"""
任务设计器 - 拖拽式任务流程设计
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Any, Optional
import json

# from ..core.action_types import ActionType, ACTION_TEMPLATES, RPAAction, create_action
from simple_rpa_tool.core.action_types import ActionType, ACTION_TEMPLATES, RPAAction, create_action
from ..core.task_engine import RPATask


class TaskDesigner:
    """任务设计器"""
    
    def __init__(self, parent, task_engine):
        """初始化任务设计器"""
        self.parent = parent
        self.task_engine = task_engine
        self.current_task: Optional[RPATask] = None
        
        # 界面组件
        self.action_palette = None
        self.design_canvas = None
        self.properties_panel = None
        
        # 设计状态
        self.selected_action = None
        self.action_widgets = {}  # action_id -> widget
        
    def create_designer_ui(self, parent_frame):
        """创建设计器界面"""
        # 创建三栏布局
        main_paned = ttk.PanedWindow(parent_frame, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：动作面板
        self.create_action_palette(main_paned)
        
        # 中间：设计画布
        self.create_design_canvas(main_paned)
        
        # 右侧：属性面板
        self.create_properties_panel(main_paned)
    
    def create_action_palette(self, parent):
        """创建动作面板"""
        palette_frame = ttk.Frame(parent, width=200)
        parent.add(palette_frame, weight=0)
        
        # 标题
        ttk.Label(palette_frame, text="动作面板", style='Heading.TLabel').pack(pady=5)
        
        # 搜索框
        search_frame = ttk.Frame(palette_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.insert(0, "搜索动作...")  # 设置默认文本
        search_entry.bind('<FocusIn>', lambda e: search_entry.delete(0, tk.END) if search_entry.get() == "搜索动作..." else None)
        search_entry.bind('<FocusOut>', lambda e: search_entry.insert(0, "搜索动作...") if not search_entry.get() else None)
        
        search_entry.pack(fill=tk.X)
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # 动作分类
        categories_frame = ttk.LabelFrame(palette_frame, text="动作分类")
        categories_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建树形视图显示动作分类
        self.action_tree = ttk.Treeview(categories_frame, show='tree')
        self.action_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加动作分类
        self.populate_action_tree()
        
        # 绑定双击事件
        self.action_tree.bind('<Double-1>', self.on_action_double_click)
        
        # 右键菜单
        self.create_action_context_menu()
    
    def create_design_canvas(self, parent):
        """创建设计画布"""
        canvas_frame = ttk.Frame(parent)
        parent.add(canvas_frame, weight=1)
        
        # 工具栏
        toolbar = ttk.Frame(canvas_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar, text="▶️ 运行", command=self.run_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="⏸️ 暂停", command=self.pause_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="⏹️ 停止", command=self.stop_task).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        ttk.Button(toolbar, text="🔍 放大", command=self.zoom_in).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="🔍 缩小", command=self.zoom_out).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="📐 适应", command=self.fit_to_window).pack(side=tk.LEFT, padx=2)
        
        # 画布区域
        canvas_container = ttk.Frame(canvas_frame)
        canvas_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建滚动画布
        self.canvas = tk.Canvas(canvas_container, bg='white', scrollregion=(0, 0, 2000, 2000))
        
        # 添加滚动条
        h_scrollbar = ttk.Scrollbar(canvas_container, orient=tk.HORIZONTAL, command=self.canvas.xview)
        v_scrollbar = ttk.Scrollbar(canvas_container, orient=tk.VERTICAL, command=self.canvas.yview)
        
        self.canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # 布局滚动条和画布
        self.canvas.grid(row=0, column=0, sticky='nsew')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        
        canvas_container.grid_rowconfigure(0, weight=1)
        canvas_container.grid_columnconfigure(0, weight=1)
        
        # 绑定画布事件
        self.canvas.bind('<Button-1>', self.on_canvas_click)
        self.canvas.bind('<B1-Motion>', self.on_canvas_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_canvas_release)
        self.canvas.bind('<Double-Button-1>', self.on_canvas_double_click)
        
        # 支持拖放
        self.canvas.drop_target_register('DND_FILES')
        
        # 绘制网格
        self.draw_grid()
    
    def create_properties_panel(self, parent):
        """创建属性面板"""
        props_frame = ttk.Frame(parent, width=250)
        parent.add(props_frame, weight=0)
        
        # 标题
        ttk.Label(props_frame, text="属性面板", style='Heading.TLabel').pack(pady=5)
        
        # 动作信息
        info_frame = ttk.LabelFrame(props_frame, text="动作信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 动作名称
        name_frame = ttk.Frame(info_frame)
        name_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(name_frame, text="名称:").pack(side=tk.LEFT)
        self.action_name_var = tk.StringVar()
        ttk.Entry(name_frame, textvariable=self.action_name_var).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        # 动作描述
        desc_frame = ttk.Frame(info_frame)
        desc_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(desc_frame, text="描述:").pack(anchor=tk.W)
        self.action_desc_text = tk.Text(desc_frame, height=3)
        self.action_desc_text.pack(fill=tk.X, pady=(2, 5))
        
        # 动作参数
        params_frame = ttk.LabelFrame(props_frame, text="参数设置")
        params_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 参数编辑区域（动态生成）
        self.params_container = ttk.Frame(params_frame)
        self.params_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 高级设置
        advanced_frame = ttk.LabelFrame(props_frame, text="高级设置")
        advanced_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 启用/禁用
        self.action_enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(advanced_frame, text="启用此动作", 
                       variable=self.action_enabled_var).pack(anchor=tk.W, padx=5, pady=2)
        
        # 超时设置
        timeout_frame = ttk.Frame(advanced_frame)
        timeout_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(timeout_frame, text="超时(秒):").pack(side=tk.LEFT)
        self.timeout_var = tk.DoubleVar(value=30.0)
        ttk.Entry(timeout_frame, textvariable=self.timeout_var, width=10).pack(side=tk.RIGHT)
        
        # 重试设置
        retry_frame = ttk.Frame(advanced_frame)
        retry_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(retry_frame, text="重试次数:").pack(side=tk.LEFT)
        self.retry_var = tk.IntVar(value=0)
        ttk.Entry(retry_frame, textvariable=self.retry_var, width=10).pack(side=tk.RIGHT)
        
        # 应用按钮
        ttk.Button(advanced_frame, text="应用更改", 
                  command=self.apply_properties).pack(pady=5)
    
    def populate_action_tree(self):
        """填充动作树"""
        # 清空现有项
        for item in self.action_tree.get_children():
            self.action_tree.delete(item)
        
        # 动作分类
        categories = {
            "鼠标操作": [ActionType.MOUSE_CLICK, ActionType.MOUSE_DOUBLE_CLICK, ActionType.MOUSE_RIGHT_CLICK, ActionType.MOUSE_MOVE],
            "键盘操作": [ActionType.KEY_PRESS, ActionType.KEY_TYPE, ActionType.KEY_COMBINATION],
            "窗口操作": [ActionType.WINDOW_ACTIVATE, ActionType.WINDOW_CLOSE, ActionType.WINDOW_MINIMIZE],
            "文件操作": [ActionType.FILE_OPEN, ActionType.FILE_SAVE, ActionType.FILE_COPY, ActionType.FILE_MOVE],
            "应用程序": [ActionType.APP_START, ActionType.APP_CLOSE],
            "等待操作": [ActionType.WAIT_TIME, ActionType.WAIT_ELEMENT, ActionType.WAIT_WINDOW],
            "流程控制": [ActionType.IF_CONDITION, ActionType.LOOP],
            "数据操作": [ActionType.DATA_READ, ActionType.DATA_WRITE, ActionType.DATA_EXPORT],
            "网页操作": [ActionType.WEB_NAVIGATE, ActionType.WEB_CLICK, ActionType.WEB_INPUT, ActionType.WEB_EXTRACT],
            "图像识别": [ActionType.IMAGE_FIND, ActionType.IMAGE_CLICK],
            "自定义": [ActionType.CUSTOM_SCRIPT]
        }
        
        for category, actions in categories.items():
            category_item = self.action_tree.insert('', 'end', text=category, open=True)
            
            for action_type in actions:
                template = ACTION_TEMPLATES.get(action_type, {})
                action_name = template.get('name', action_type.value)
                self.action_tree.insert(category_item, 'end', text=action_name, values=(action_type.value,))
    
    def on_search_change(self, event):
        """搜索框内容改变"""
        search_text = self.search_var.get().lower()
        # TODO: 实现搜索过滤功能
    
    def on_action_double_click(self, event):
        """动作双击事件"""
        selection = self.action_tree.selection()
        if not selection:
            return
        
        item = selection[0]
        values = self.action_tree.item(item, 'values')
        
        if values:  # 是动作项，不是分类项
            action_type_str = values[0]
            try:
                action_type = ActionType(action_type_str)
                self.add_action_to_canvas(action_type)
            except ValueError:
                pass
    
    def add_action_to_canvas(self, action_type: ActionType, x: int = None, y: int = None):
        """添加动作到画布"""
        if not self.current_task:
            messagebox.showwarning("警告", "请先创建或打开一个任务")
            return
        
        # 创建动作
        action = create_action(action_type, {})
        
        # 添加到任务
        self.current_task.add_action(action)
        
        # 在画布上绘制动作
        if x is None or y is None:
            # 自动计算位置
            action_count = len(self.current_task.actions)
            x = 50
            y = 50 + (action_count - 1) * 80
        
        self.draw_action_on_canvas(action, x, y)
    
    def draw_action_on_canvas(self, action: RPAAction, x: int, y: int):
        """在画布上绘制动作"""
        # 绘制动作框
        width, height = 200, 60
        
        # 动作背景
        rect = self.canvas.create_rectangle(x, y, x + width, y + height, 
                                          fill='lightblue', outline='blue', width=2)
        
        # 动作名称
        text = self.canvas.create_text(x + width//2, y + 20, text=action.name, 
                                     font=('Arial', 10, 'bold'))
        
        # 动作描述
        desc_text = action.description[:30] + "..." if len(action.description) > 30 else action.description
        desc = self.canvas.create_text(x + width//2, y + 40, text=desc_text, 
                                     font=('Arial', 8), fill='gray')
        
        # 保存组件引用
        self.action_widgets[action.id] = {
            'action': action,
            'rect': rect,
            'text': text,
            'desc': desc,
            'x': x,
            'y': y,
            'width': width,
            'height': height
        }
        
        # 绑定点击事件
        for widget in [rect, text, desc]:
            self.canvas.tag_bind(widget, '<Button-1>', lambda e, aid=action.id: self.select_action(aid))
            self.canvas.tag_bind(widget, '<Double-Button-1>', lambda e, aid=action.id: self.edit_action(aid))
    
    def draw_grid(self):
        """绘制网格"""
        # 绘制网格线
        grid_size = 20
        canvas_width = 2000
        canvas_height = 2000
        
        # 垂直线
        for x in range(0, canvas_width, grid_size):
            self.canvas.create_line(x, 0, x, canvas_height, fill='lightgray', width=1)
        
        # 水平线
        for y in range(0, canvas_height, grid_size):
            self.canvas.create_line(0, y, canvas_width, y, fill='lightgray', width=1)
    
    def select_action(self, action_id: str):
        """选择动作"""
        # 取消之前的选择
        if self.selected_action:
            prev_widget = self.action_widgets.get(self.selected_action)
            if prev_widget:
                self.canvas.itemconfig(prev_widget['rect'], outline='blue', width=2)
        
        # 选择新动作
        self.selected_action = action_id
        widget = self.action_widgets.get(action_id)
        if widget:
            self.canvas.itemconfig(widget['rect'], outline='red', width=3)
            self.load_action_properties(widget['action'])
    
    def edit_action(self, action_id: str):
        """编辑动作"""
        widget = self.action_widgets.get(action_id)
        if widget:
            # TODO: 打开动作编辑对话框
            messagebox.showinfo("提示", f"编辑动作: {widget['action'].name}")
    
    def load_action_properties(self, action: RPAAction):
        """加载动作属性到属性面板"""
        # 基本信息
        self.action_name_var.set(action.name)
        self.action_desc_text.delete(1.0, tk.END)
        self.action_desc_text.insert(1.0, action.description)
        
        # 高级设置
        self.action_enabled_var.set(action.enabled)
        self.timeout_var.set(action.timeout)
        self.retry_var.set(action.retry_count)
        
        # 清空参数容器
        for widget in self.params_container.winfo_children():
            widget.destroy()
        
        # 动态生成参数编辑控件
        self.create_parameter_widgets(action)
    
    def create_parameter_widgets(self, action: RPAAction):
        """创建参数编辑控件"""
        template = ACTION_TEMPLATES.get(action.type, {})
        parameters = template.get('parameters', [])
        
        for i, param in enumerate(parameters):
            param_frame = ttk.Frame(self.params_container)
            param_frame.pack(fill=tk.X, pady=2)
            
            # 参数标签
            label_text = param.name
            if param.required:
                label_text += " *"
            ttk.Label(param_frame, text=label_text).pack(side=tk.LEFT)
            
            # 参数输入控件
            current_value = action.parameters.get(param.name, param.default_value)
            
            if param.type == 'bool':
                var = tk.BooleanVar(value=current_value)
                ttk.Checkbutton(param_frame, variable=var).pack(side=tk.RIGHT)
            elif param.options:  # 下拉选择
                var = tk.StringVar(value=current_value)
                combo = ttk.Combobox(param_frame, textvariable=var, values=param.options, state="readonly")
                combo.pack(side=tk.RIGHT)
            else:  # 文本输入
                var = tk.StringVar(value=str(current_value) if current_value is not None else "")
                entry = ttk.Entry(param_frame, textvariable=var)
                entry.pack(side=tk.RIGHT, fill=tk.X, expand=True)
            
            # 保存变量引用以便后续获取值
            setattr(self, f"param_{param.name}_var", var)
    
    def apply_properties(self):
        """应用属性更改"""
        if not self.selected_action:
            return
        
        widget = self.action_widgets.get(self.selected_action)
        if not widget:
            return
        
        action = widget['action']
        
        # 更新基本信息
        action.name = self.action_name_var.get()
        action.description = self.action_desc_text.get(1.0, tk.END).strip()
        
        # 更新高级设置
        action.enabled = self.action_enabled_var.get()
        action.timeout = self.timeout_var.get()
        action.retry_count = self.retry_var.get()
        
        # 更新参数
        template = ACTION_TEMPLATES.get(action.type, {})
        parameters = template.get('parameters', [])
        
        for param in parameters:
            var = getattr(self, f"param_{param.name}_var", None)
            if var:
                value = var.get()
                if param.type == 'int':
                    try:
                        value = int(value)
                    except ValueError:
                        value = param.default_value
                elif param.type == 'float':
                    try:
                        value = float(value)
                    except ValueError:
                        value = param.default_value
                
                action.parameters[param.name] = value
        
        # 更新画布显示
        self.canvas.itemconfig(widget['text'], text=action.name)
        desc_text = action.description[:30] + "..." if len(action.description) > 30 else action.description
        self.canvas.itemconfig(widget['desc'], text=desc_text)
        
        messagebox.showinfo("成功", "属性已更新")
    
    # 画布事件处理
    def on_canvas_click(self, event):
        """画布点击事件"""
        # 取消选择
        if self.selected_action:
            widget = self.action_widgets.get(self.selected_action)
            if widget:
                self.canvas.itemconfig(widget['rect'], outline='blue', width=2)
            self.selected_action = None
    
    def on_canvas_drag(self, event):
        """画布拖拽事件"""
        pass
    
    def on_canvas_release(self, event):
        """画布释放事件"""
        pass
    
    def on_canvas_double_click(self, event):
        """画布双击事件"""
        pass
    
    # 工具栏事件处理
    def run_task(self):
        """运行任务"""
        if self.current_task:
            self.task_engine.run_task(self.current_task.id)
            messagebox.showinfo("提示", "任务已开始运行")
        else:
            messagebox.showwarning("警告", "没有可运行的任务")
    
    def pause_task(self):
        """暂停任务"""
        messagebox.showinfo("提示", "暂停功能正在开发中")
    
    def stop_task(self):
        """停止任务"""
        if self.current_task:
            self.task_engine.stop_task(self.current_task.id)
            messagebox.showinfo("提示", "任务已停止")
    
    def zoom_in(self):
        """放大画布"""
        self.canvas.scale('all', 0, 0, 1.2, 1.2)
    
    def zoom_out(self):
        """缩小画布"""
        self.canvas.scale('all', 0, 0, 0.8, 0.8)
    
    def fit_to_window(self):
        """适应窗口"""
        # TODO: 实现适应窗口功能
        pass
    
    def create_action_context_menu(self):
        """创建动作右键菜单"""
        self.action_menu = tk.Menu(self.action_tree, tearoff=0)
        self.action_menu.add_command(label="添加到画布", command=self.add_selected_action)
        self.action_menu.add_command(label="查看帮助", command=self.show_action_help)
        
        self.action_tree.bind('<Button-3>', self.show_action_menu)
    
    def show_action_menu(self, event):
        """显示动作右键菜单"""
        try:
            self.action_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.action_menu.grab_release()
    
    def add_selected_action(self):
        """添加选中的动作"""
        selection = self.action_tree.selection()
        if selection:
            self.on_action_double_click(None)
    
    def show_action_help(self):
        """显示动作帮助"""
        messagebox.showinfo("帮助", "动作帮助功能正在开发中")
    
    def set_current_task(self, task: RPATask):
        """设置当前任务"""
        self.current_task = task
        self.refresh_canvas()
    
    def refresh_canvas(self):
        """刷新画布"""
        # 清空画布
        self.canvas.delete('all')
        self.action_widgets.clear()
        self.selected_action = None
        
        # 重新绘制网格
        self.draw_grid()
        
        # 绘制任务中的所有动作
        if self.current_task:
            for i, action in enumerate(self.current_task.actions):
                x = 50
                y = 50 + i * 80
                self.draw_action_on_canvas(action, x, y)
